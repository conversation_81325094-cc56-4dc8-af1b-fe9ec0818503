import { middleware } from "#start/kernel"
import router from "@adonisjs/core/services/router"
import adminVendorOrdersRoutes from "./vendors_orders_router.js";

const AdminVendorController = () => import("#adminControllers/vendors/admin_vendor_controller");
const AdminVendorEarningController = () => import("#adminControllers/vendors/admin_vendor_earning_controller");
const AdminVendorPaymentController = () => import("#adminControllers/vendors/admin_vendor_payment_controller");
const AdminVendorPaymentMethodController = () => import("#adminControllers/vendors/admin_vendor_payment_method_controller");

export default function adminVendorRoutes() {

  router
    .group(() => {
      router.get('vendors/settings', [AdminVendorController, 'settings']);

      router.get('vendors/earnings', [AdminVendorEarningController, 'index']);
      router.get('vendors/earnings/:id', [AdminVendorEarningController, 'show']);
      router.put('vendors/earnings/:id', [AdminVendorEarningController, 'update']);
      router.delete('vendors/earnings/:id', [AdminVendorEarningController, 'destroy']);
      router.get('vendors/:vendorId/earnings', [AdminVendorEarningController, 'index']);

      router.get('vendors/:vendorId/payments', [AdminVendorPaymentController, 'index']);
      router.post('vendors/:vendorId/payments', [AdminVendorPaymentController, 'store']);
      router.delete('vendors/payments/:id', [AdminVendorPaymentController, 'destroy']);

      router.get('vendors/:vendorId/payment-methods', [AdminVendorPaymentMethodController, 'index'])

      router.get('vendors/:vendorId/stats', [AdminVendorController, 'stats']);
      router.get('vendors/:vendorId/balance', [AdminVendorController, 'getBalance']);

      router.get('vendors/warehouses', [AdminVendorController, 'selectShopifyWarehouseLocation'])
      router.put('vendors/warehouses', [AdminVendorController, 'changeShopifyWarehouseLocation'])

      router
        .group(() => {
          adminVendorOrdersRoutes()
        })
        .prefix('vendors')

      router.resource('vendors', AdminVendorController);
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }));
}
