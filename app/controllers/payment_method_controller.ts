import ZnAffiliate from "#models/zn_affiliate"
import ZnUser from "#models/zn_user"
import AffiliationPaymentMethodService from "#services/affiliation/affiliation_payment_method_service"
import { addPaymentMethodValidator, updatePaymentMethodValidator } from "#validators/affiliate"
import { HttpContext } from "@adonisjs/core/http"
import logger from "@adonisjs/core/services/logger"

export default class PaymentMethodController {
  private paymentMethodService: AffiliationPaymentMethodService;

  constructor() {
    this.paymentMethodService = new AffiliationPaymentMethodService();
  }

  /**
   * @index
   * @tag Payment Methods
   * @summary Get list of payment methods
   * @responseBody 201 - <ZnPaymentMethod[]>
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async index({ auth, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser
      const affiliate = await ZnAffiliate.findByOrFail('userId', user.id);
      return await this.paymentMethodService.getAllPaymentMethods(affiliate.id);
    } catch (error) {
      logger.debug('Error: %s', error.message);
      return response.badRequest(error)
    }
  }

  /**
   * @show
   * @tag Payment Methods
   * @summary Get a payment methods
   * @responseBody 201 - <ZnPaymentMethod>
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async show({ params, response }: HttpContext) {
    try {
      const paymentMethodId = params.id;
      return await this.paymentMethodService.getPaymentMethodById(paymentMethodId);

    } catch (error) {
      logger.debug('Error: %s', error.message);
      return response.badRequest(error)
    }
  }

  /**
   * @store
   * @tag Payment Methods
   * @summary Add new payment method
   * @requestBody {"paymentType": "PAYPAL", "isDefault": true, "paypalEmail": ""}
   * @responseBody 201 - <ZnPaymentMethod>
   * @responseBody 400 - Invalid credentials | Code incorrect | Code is expired - Bad Request
   */
  async store({ auth, request, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser
      const payload = await addPaymentMethodValidator.validate(request.all())

      const affiliate = await ZnAffiliate.findByOrFail('userId', user.id);
      return await this.paymentMethodService.createPaymentMethod(affiliate.id, payload);
    } catch (error) {
      logger.debug('Error: %s', error.message);
      if (error.message.includes('already been added')) {
        return response.notModified();
      }
      return response.badRequest(error)
    }
  }

  /**
   * @update
   * @tag Payment Methods
   * @summary Update payment method
   * @requestBody {"paypalEmail": "<EMAIL>", "isDefault":true}
   * @responseBody 201 - <ZnPaymentMethod>
   * @responseBody 400 - Invalid credentials | Code incorrect | Code is expired - Bad Request
   */
  async update({ params, request, response }: HttpContext) {
    try {
      const paymentMethodId = params.id;
      const payload = await updatePaymentMethodValidator.validate(request.all())
      return await this.paymentMethodService.updatePaymentMethod(paymentMethodId, payload);
    } catch (error) {
      logger.debug('Error: %s', error.message);
      return response.badRequest(error)
    }
  }

  /**
   * @setDefaultMethod
   * @tag Payment Methods
   * @summary Set default payment method
   * @responseBody 201 - <ZnPaymentMethod[]>
   * @responseBody 400 - Invalid credentials | Code incorrect | Code is expired - Bad Request
   */
  async setDefaultMethod({ auth, params, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser
      const paymentMethodId = params.id;
      return await this.paymentMethodService.setDefaultMethod(paymentMethodId);
    } catch (error) {
      logger.debug('Error: %s', error.message);
      return response.badRequest(error)
    }
  }

  /**
   * @destroy
   * @tag Payment Methods
   * @summary Delete a payment method
   * @responseBody 201 - <ZnPaymentMethod>
   * @responseBody 400 - Invalid credentials | Code incorrect | Code is expired - Bad Request
   */
  async destroy({ params, response }: HttpContext) {
    try {
      const paymentMethodId = params.id;
      if (!paymentMethodId) return response.badRequest('Payment method ID is required')
      return await this.paymentMethodService.deletePaymentMethodId(paymentMethodId);
    } catch (error) {
      logger.debug('Error: %s', error.message);
      return response.badRequest(error)
    }
  }
}