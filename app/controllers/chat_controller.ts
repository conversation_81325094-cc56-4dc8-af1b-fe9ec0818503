import { STREAM_EVENT } from '#constants/stream_events'
import { TRACKING_ACTION } from '#constants/tracking'
import Cha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '#jobs/chat_delete_job'
import Chat<PERSON><PERSON>Job from '#jobs/chat_event_job'
import ChatMessageJob from '#jobs/chat_message_job'
import StreamViewerCountTrackingJob from '#jobs/stream_viewer_count_tracking_job'
import ZnChatMessage from '#models/zn_chat_message'
import ZnChatRoom from '#models/zn_chat_room'
import ZnStream from '#models/zn_stream'
import ZnUser from '#models/zn_user'
import { IvsService } from '#services/aws/ivs_service'
import { IvschatService } from '#services/aws/ivschat_service'
import JwtService from '#services/jwt_service'
import { TrackingService } from '#services/tracking_service'
import { connectChatRoomValidator } from '#validators/chat_validator'
import { HttpContext } from '@adonisjs/core/http'
import queue from '@rlanz/bull-queue/services/main'
import { DateTime } from 'luxon'
import moment from 'moment'

export default class ChatController {
    private ivsChatService: IvschatService

    constructor() {
        this.ivsChatService = new IvschatService()
    }

    /**
     * @createChatRoom
     * @tag Chat
     * @summary Create Chat Room
     * @requestBody {"name":""}
     * @responseBody 200 - <ZnChatRoom> - Create Chat Room
     */
    async createChatRoom({ request, response }: HttpContext) {
        const data = request.body()

        const room = await this.ivsChatService.createRoom({ name: data.name })

        const dbRoom = await ZnChatRoom.create({
            arn: room.arn,
            name: room.name,
        })

        return response.ok(dbRoom)
    }

    /**
     * @createChatToken
     * @tag Chat
     * @summary Chat Token provider
     * @paramPath id - ID of Chat Room - @required
     * @responseBody 200 - {"token":"","tokenExpirationTime":"2025-02-18T22:44:57.000Z","tokenExpirationTime","2025-02-18T22:44:57.000Z"} - ChatToken provider for IVS ChatRoom
     */
    async createChatToken({ params, request, response }: HttpContext) {
        const roomId = params.id

        const room = await ZnChatRoom.find(roomId)

        if (!room) { return response.notFound({ message: 'Chat Room not found' }) }

        let requestUserId
        const authToken = request.header('Authorization') as string
        JwtService.decodeToken(authToken, (decodedToken: Partial<{ userId: string }>) => {
            if (decodedToken) {
                requestUserId = decodedToken.userId
            }
        })

        let attributes: any = {
            type: 'guest'
        }
        if (requestUserId) {
            const user = await ZnUser.find(requestUserId)
            attributes = {
                type: 'user',
                email: user?.email,
                firstName: user?.firstName,
                lastName: user?.lastName,
                fullname: user?.fullname,
                avatarUrl: user?.avatarUrl,
                avatarMediaUrl: user?.avatarMedia?.url
            }
        }

        const token = await this.ivsChatService.createChatToken({
            roomArn: room.arn,
            userId: requestUserId || "Guest",
            capabilities: requestUserId ? ['SEND_MESSAGE', 'DELETE_MESSAGE'] : [],
            attributes
        })

        return response.ok(token)
    }

    /**
     * @connectChatRoom
     * @tag Chat
     * @summary Log connections to Chat Room
     * @description Log connections to Chat Room descriptively
     * @paramPath id - ID of Chat Room - @required
     * @requestBody {"type":"connect|disconnect"}
     */
    async connectChatRoom({ params, request, response }: HttpContext) {
        try {
            const roomId = params.id
            const room = await ZnChatRoom.find(roomId)
            if (!room) { return response.notFound({ message: 'Chat Room not found' }) }

            const data = request.all()
            const payload = await connectChatRoomValidator.validate(data)

            let requestUserId
            const authToken = request.header('Authorization') as string
            JwtService.decodeToken(authToken, (decodedToken: Partial<{ userId: string }>) => {
                if (decodedToken) {
                    requestUserId = decodedToken.userId
                }
            })

            const stream = await ZnStream.findBy({ roomId })
            if (stream?.isLive) {
                const countChange = payload.type == 'connect' ? 1 : -1

                const trackingService = new TrackingService()

                const streamView = await trackingService.getView({
                    resourceId: stream.id,
                    action: TRACKING_ACTION.GET_STREAM_VIEW,
                })

                const currentViewerCount = Math.max(streamView[TRACKING_ACTION.GET_STREAM_VIEW].count + countChange, 0)
                const currentCount = {
                    current: currentViewerCount,
                    change: countChange,
                }

                const metadata = {
                    event: STREAM_EVENT.VIEWER_COUNT,
                    timestamp: moment.now(),
                    viewerCount: currentCount.current,
                }

                const metadataString = JSON.stringify(metadata)

                const ivsService = new IvsService()
                await ivsService.putMetadata(stream.channelArn, metadataString)

                await queue.dispatch(
                    StreamViewerCountTrackingJob,
                    {
                        streamId: stream.id,
                        currentCount,
                        userId: requestUserId,
                        createdAt: DateTime.now().toISO(),
                    },
                    { queueName: 'tracking' }
                )

            }

            return response.ok("StreamViewerCountTrackingJob Sent")

        } catch (error) {
            return response.internalServerError(error)
        }
    }

    /**
     * @sendChatEvent
     * @tag Chat
     * @summary Send chat event
     * @description Type of events:<br>"heart" : Like
     * @paramPath id - ID of Chat Room - @required
     * @requestBody {"eventName":"heart","attributes":{}}
     * @responseBody 200 - ChatEventJob Sent. - Send chat event descriptively
     */
    async sendChatEvent({ params, request, response }: HttpContext) {
        const roomId = params.id

        const room = await ZnChatRoom.find(roomId)

        if (!room) { return }

        const data = request.body() as any

        let requestUserId
        const authToken = request.header('Authorization') as string
        JwtService.decodeToken(authToken, (decodedToken: Partial<{ userId: string }>) => {
            if (decodedToken) {
                requestUserId = decodedToken.userId
            }
        })

        const newData = {
            ...data,
            attributes: {
                ...data.attributes,
                userId: requestUserId
            }
        }

        const ivschatService = new IvschatService()
        await ivschatService.sendEvent(room.arn, newData.eventName, newData.attributes)

        await queue.dispatch(
            ChatEventJob,
            { roomId, data: newData, },
            { queueName: 'liveStream' }
        )

        return response.ok("StreamChatEventJob Sent.")
    }

    /**
     * @getMessages
     * @tag Chat
     * @summary Get Chat Room Messages
     * @paramPath id - ID of Chat Room - @required
     * @paramQuery page - Page Number (default 1) - @type(number)
     * @paramQuery limit - Page Limit (default 10) - @type(number)
     * @responseBody 200 - <ZnChatMessage[]>.paginated() - Get Chat Room Messages, newest to oldest
     */
    async getMessages({ params, request, response }: HttpContext) {
        const {
            page = 1,
            limit = 10,
            filterIvsIds = []
        } = request.qs()

        try {
            const roomId = params.id
            const room = await ZnChatRoom.find(roomId)
            if (!room) { return response.notFound("Chat Room Not Found") }

            const messages = await ZnChatMessage.query()
                .where({ roomId })
                .preload('user', (userQuery) => {
                    userQuery.preload('avatarMedia')
                })
                .preload('admin', (adminQuery) => {
                    adminQuery.preload('avatar')
                })
                .preload('children', (childrenQuery) => {
                    childrenQuery
                        .preload('user', (userQuery) => {
                            userQuery.preload('avatarMedia')
                        })
                        .preload('admin', (adminQuery) => {
                            adminQuery.preload('avatar')
                        })
                        .orderBy('createdAt', 'asc')
                })
                .whereNull('parentMessageId')
                .whereNotIn('ivsChatMessageId', filterIvsIds)
                .withCount('children')
                .orderBy('createdAt', 'desc')
                .paginate(page, limit)

            return response.ok(messages)
        } catch (error) {
            return response.internalServerError("Something went wrong", error)
        }
    }

    /**
     * @getChildren
     * @tag Chat
     * @summary Get child messages of chat
     * @paramPath id - IVS ID or UUID of Chat Message - @required
     * @paramQuery page - Page Number (default 1) - @type(number)
     * @paramQuery limit - Page Limit (default 10) - @type(number)
     * @paramQuery filterIvsIds - IVS IDs of Chat Messages to be filtered (e.g. "wNbmMLv3EuWl") - @type(array)
     * @responseBody 200 - <ZnChatMessage[]>.paginated() - Send chat event descriptively
     */
    async getChildren({ params, request, response }: HttpContext) {
        const {
            page = 1,
            limit = 10,
            filterIvsIds = []
        } = request.qs()

        try {
            const messageId = params.id
            const message = await ZnChatMessage.query()
                .where({ id: messageId })
                .orWhere({ ivsChatMessageId: messageId })
                .first()
            if (!message) { return response.notFound("Chat Message Not Found") }

            const children = await ZnChatMessage.query()
                .preload('parentMessage')
                .preload('user', (userQuery) => {
                    userQuery.preload('avatarMedia')
                })
                .preload('admin', (adminQuery) => {
                    adminQuery.preload('avatar')
                })
                .where({ parentMessageId: message.id })
                .whereNotIn('ivsChatMessageId', filterIvsIds)
                .orderBy('createdAt', 'asc')
                .paginate(page, limit)

            return response.ok(children)

        } catch (error) {
            return response.internalServerError("Something went wrong", error)
        }
    }

    /**
     * @createChatMessage
     * @tag Chat
     * @summary Create chat message
     * @paramPath id - ID of Chat Room - @required
     * @requestBody {"id":"wNbmMLv3EuWl","content":"A message","attributes":{"parentMessageIvsId":"LWuE3vLMmbNw"}}
     * @responseBody 200 - StreamCommentJob Sent. - Send job to save chat message as post comment
     */
    async createChatMessage({ auth, params, request, response }: HttpContext) {
        const roomId = params.id

        try {
            const room = await ZnChatRoom.find(roomId)
            if (!room) { return response.notFound("Chat Room Not Found") }

            const data = request.body() as any

            if (data.attributes?.parentMessageIvsId) {
                const reply = await ZnChatMessage.findBy({ ivsChatMessageId: data.attributes?.parentMessageIvsId })
                if (!reply) { return response.notFound("Chat Reply Message Not Found") }
            }

            const currentUser = auth.getUserOrFail() as ZnUser

            await queue.dispatch(
                ChatMessageJob,
                {
                    roomId,
                    data: {
                        ...data,
                        sender: {
                            userId: currentUser.id,
                        }
                    },
                },
                { queueName: 'liveStream' }
            )

            return response.ok("StreamCommentJob Sent.")

        } catch (error) {
            return response.internalServerError("Something went wrong", error)
        }
    }

    /**
     * @deleteChatMessage
     * @tag Chat
     * @summary Delete chat message
     * @paramPath id - IVS ID of Chat Message - @required @example(wNbmMLv3EuWl)
     * @responseBody 200 - StreamChatDeleteJob Sent. - Delete Chat Message
     * @responseBody 403 - Cannot delete others messages - Forbidden access
     * @responseBody 404 - Chat Message Not Found - Not Found
     */
    async deleteChatMessage({ auth, params, response }: HttpContext) {
        const ivsChatMessageId = params.id

        const message = await ZnChatMessage.findBy({ ivsChatMessageId })

        if (!message) { return response.notFound("Chat Message Not Found") }

        const currentUser = auth.getUserOrFail() as ZnUser

        if (message.userId != currentUser.id) { return response.forbidden("Cannot delete others messages") }

        await queue.dispatch(
            ChatDeleteJob,
            { ivsChatMessageId },
            { queueName: 'liveStream' }
        )

        return response.ok("StreamChatDeleteJob Sent.")
    }
}
