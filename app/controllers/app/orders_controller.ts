import { paymentConfig } from '#config/payment'
import { EOrderStatus } from '#constants/order'
import OrderRefund<PERSON>ob from '#jobs/order_refund_job'
import ZnOrder from '#models/zn_order'
import ZnOrderFulfillment from '#models/zn_order_fulfillment'
import { PaymentHandleService } from '#services/payment/payment_handle_service'
import { ShopifyService } from '#services/shopify/shopify_service'
import type { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'
import queue from '@rlanz/bull-queue/services/main'
import { cancelOrderValidator } from '../../../admin/validators/order/order_validator.js'
import { getShopifyOrderId } from '../../../services/commons.js'

export default class OrdersController {
  async list({ request, response, user }: HttpContext) {
    if (!user) {
      return response.unauthorized('Unauthorized')
    }
    const { page = 1, pageSize = 10, status } = request.qs()
    const queryOrder = ZnOrder.query()
      .where('userId', user.id)
      .withCount('orderDetails')
      .preload('firstOrderDetail', (query) => {
        query.preload('variant', (variantQuery) => {
          variantQuery.preload('image').preload('product')
        })
      })
      .preload('fulfillments', (fulfillmentQuery) => {
        fulfillmentQuery
          .orderBy('createdAt', 'desc')
          .limit(1)
          .preload('trackingHistory', (trackingHistoryQuery) => {
            trackingHistoryQuery.orderBy('createdAt', 'desc').limit(1)
          })
      })

    switch (status) {
      // case EOrderStatus.Ongoing:
      case EOrderStatus.Completed:
      // case EOrderStatus.Shipped:
      case EOrderStatus.Cancelled:
        queryOrder.where('status', status)
        break
      default:
        queryOrder.where((query) =>
          query.where('status', '').orWhereNull('status').orWhere('status', EOrderStatus.Processing)
        )
    }

    const orders = await queryOrder.orderBy('createdAt', 'desc').paginate(page, pageSize)

    return response.ok(orders)
  }

  async count({ request, response, user }: HttpContext) {
    if (!user) {
      return response.unauthorized('Unauthorized')
    }
    const { status } = request.qs()
    const queryOrder = ZnOrder.query().where('userId', user.id)

    switch (status) {
      case EOrderStatus.Ongoing:
      case EOrderStatus.Completed:
      case EOrderStatus.Shipped:
      case EOrderStatus.Cancelled:
        queryOrder.where('status', status)
        break
      default:
        queryOrder.where((query) =>
          query.where('status', '').orWhereNull('status').orWhere('status', EOrderStatus.Processing)
        )
    }

    const orders = await queryOrder.select(db.raw('count(id) as total')).first()

    return response.ok({ total: orders?.$extras?.['total'] || 0 })
  }

  async show({ params, response, user }: HttpContext) {
    if (!user) {
      return response.unauthorized('Unauthorized')
    }
    const order = await ZnOrder.query()
      .where({ userId: user.id })
      .where((query) => {
        query.where('id', params.id).orWhere('shopifyId', getShopifyOrderId(params.id))
      })
      .preload('shippingAddress')
      .preload('billingAddress')
      .preload('orderDiscounts')
      .preload('orderDetails', (query) => {
        query.preload('variant', (variantQuery) => {
          variantQuery.preload('image').preload('product')
        })
      })
      .preload('fulfillments', (fulfillmentQuery) => {
        fulfillmentQuery
          .preload('orderDetails', (orderDetailQuery) => {
            orderDetailQuery.preload('variant', (variantQuery) => {
              variantQuery.preload('image').preload('product')
            })
          })
          .orderBy('createdAt', 'desc')
          .preload('trackingHistory', (trackingHistoryQuery) => {
            trackingHistoryQuery.orderBy('createdAt', 'desc').limit(1)
          })
      })
      .first()

    if (!order) {
      return response.notFound('Order not found')
    }

    return response.ok(order.toJSON())
  }

  async cancel({ params, response, user, request }: HttpContext) {
    if (!user) {
      return response.unauthorized('Unauthorized')
    }
    const order = await ZnOrder.query().where({ id: params.id, userId: user.id }).first()

    if (!order) {
      return response.notFound('Order not found')
    }

    if (order.cancelledAt) {
      return response.badRequest('Order already cancelled')
    }
    if (order.closedAt) {
      return response.badRequest('Order already completed')
    }
    if (!order.shopifyId) {
      return response.badRequest('Order not synced with Shopify')
    }
    const payload = await request.validateUsing(cancelOrderValidator)
    const shopifyService = new ShopifyService()
    await shopifyService.cancelOrder({ orderId: order.shopifyId, note: payload.reason })

    // Refund Payment
    if (paymentConfig.applyZurnoPayment) {
      await queue.dispatch(OrderRefundJob, { orderId: order.id })
      const paymentHandleService = new PaymentHandleService()
      try {
        // Refund Payment
        await paymentHandleService.refundPayment(order.id)
      } catch (error) {
        try {
          await paymentHandleService.voidTransaction(order.id)
        } catch (error) {
          console.error(error)
        }
      }
    }

    return response.ok('Cancel successfully')
  }

  async tracking({ params, response, user }: HttpContext) {
    if (!user) {
      return response.unauthorized('Unauthorized')
    }
    const fulfillments = await ZnOrderFulfillment.query()
      .where({ orderId: params.id })
      .preload('trackingHistory', (query) => {
        query.orderBy('statusDate', 'desc')
      })
      .orderBy('createdAt', 'desc')

    return response.ok(fulfillments)
  }

  async trackingByFulfillmentId({ params, response, user }: HttpContext) {
    if (!user) {
      return response.unauthorized('Unauthorized')
    }
    const fulfillment = await ZnOrderFulfillment.query()
      .where({ id: params.id })
      .preload('trackingHistory', (query) => {
        query.orderBy('statusDate', 'desc')
      })
      .preload('order', (query) => {
        query.preload('shippingAddress')
        query.preload('billingAddress')
      })
      .preload('orderDetails', (orderDetailQuery) => {
        orderDetailQuery.preload('variant', (variantQuery) => {
          variantQuery.preload('image').preload('product')
        })
      })
      .first()

    return response.ok(fulfillment)
  }
}
