import {BaseEmbeddingJob} from "#jobs/base_embedding_job";
import ZnProduct from "#models/zn_product";
import ZnCollection from "#models/zn_collection";
import SparseProductCollectionService from "../services/pinecone/sparse_product_collection_embedding_service.js";

export default class UpdateSparseProductCollectionEmbeddingJob extends BaseEmbeddingJob<ZnProduct | ZnCollection> {
  protected embeddingService = new SparseProductCollectionService()
  static get $$filepath() { return import.meta.url }

  protected async lookup(ids: string[]): Promise<(ZnProduct | ZnCollection)[]> {
    const [products, collections] = await Promise.all([
      ZnProduct
        .query()
        .whereIn('id', ids)
        .preload('tags')
        .preload('collections')
        .preload('vendor')
        .preload('productType'),
      ZnCollection.query().whereIn('id', ids),
    ])

    return [...products, ...collections]
  }
}
