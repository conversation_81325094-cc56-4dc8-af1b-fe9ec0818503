import ZnTracking from '#models/zn_tracking'
import { TrackingService } from '#services/tracking_service'
import { Job } from '@rlanz/bull-queue'
import queue from '@rlanz/bull-queue/services/main'
import { TRACKING_ACTION } from '../constants/tracking.js'
import CreateTrackingJob from './create_tracking_job.js'
import UpdateTrackingSnapshotJob from './update_tracking_snapshot_job.js'
interface StreamViewerCountTrackingJobPayload {
  streamId: string,
  currentCount: any,
  userId?: string,
  createdAt?: string,
}
export default class StreamViewerCountTrackingJob extends Job {
  private trackingService = new TrackingService()
  // This is the path to the file that is used to create the job
  static get $$filepath() {
    return import.meta.url
  }
  /**
   * Base Entry point
   */
  async handle(payload: StreamViewerCountTrackingJobPayload) {
    const lastestStreamViewTracking = await ZnTracking.query()
      .where({
        resource: this.trackingService.getResourceByAction(TRACKING_ACTION.GET_STREAM_VIEW),
        resourceId: payload.streamId,
        action: TRACKING_ACTION.GET_STREAM_VIEW,
      })
      .orderBy('createdAt', 'desc')
      .first()

    const latestCount = {
      highest: lastestStreamViewTracking?.details?.highest || 0,
      total: lastestStreamViewTracking?.details?.total || 0,
    }

    const changeCount = payload.currentCount.change
    const currentCount = payload.currentCount.current
    const highestCount = Math.max(latestCount.highest, currentCount)
    const totalCount = latestCount.total + Math.max(changeCount, 0)

    await queue.dispatch(
      CreateTrackingJob,
      {
        userId: payload.userId || "",
        resourceId: payload.streamId,
        resource: this.trackingService.getResourceByAction(TRACKING_ACTION.GET_STREAM_VIEW),
        action: TRACKING_ACTION.GET_STREAM_VIEW,
        details: JSON.stringify({
          change: changeCount,
          current: currentCount,
          highest: highestCount,
          total: totalCount,
        }),
        createdAt: payload.createdAt,
      },
      { queueName: 'tracking' }
    )
    
    await queue.dispatch(
      UpdateTrackingSnapshotJob,
      {
        resourceId: payload.streamId,
        resource: this.trackingService.getResourceByAction(TRACKING_ACTION.GET_STREAM_VIEW),
        action: TRACKING_ACTION.GET_STREAM_VIEW,
        userId: payload.userId || "",
        countChange: changeCount
      },
      { queueName: 'tracking' }
    )
  }
  /**
   * This is an optional method that gets called when the retries has exceeded and is marked failed.
   */
  async rescue() { }
}