import { EOrderStatus } from '#constants/order'
import AppModel from '#models/app_model'
import ZnAddress from '#models/zn_address'
import ZnOrderDetail from '#models/zn_order_detail'
import ZnUser from '#models/zn_user'
import { belongsTo, column, computed, hasMany, hasOne } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany, HasOne } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import ZnAffiliateCommission from './zn_affiliate_commission.js'
import ZnOrderDiscount from './zn_order_discount.js'
import ZnOrderFulfillment from './zn_order_fulfillment.js'
import ZnTransaction from './zn_transaction.js'
import ZnVendorOrder from './zn_vendor_order.js'

export default class ZnOrder extends AppModel {
  @column({ columnName: 'shopifyId' })
  declare shopifyId: string

  @column()
  declare name: string

  @column()
  declare email: string

  @column()
  declare status: EOrderStatus | null | ''

  @column({ columnName: 'financialStatus' })
  declare financialStatus: string

  @column({
    columnName: 'fulfillmentStatus',
    serialize: (value) => value || 'unfulfilled',
  })
  declare fulfillmentStatus: string

  @column.dateTime({ columnName: 'fulfilledAt' })
  declare fulfilledAt: DateTime | null

  @column.dateTime({ columnName: 'cancelledAt' })
  declare cancelledAt: DateTime | null

  @column.dateTime({ columnName: 'closedAt' })
  declare closedAt: DateTime | null

  @column({
    columnName: 'totalPrice',
    consume: (value: string) => parseFloat(value),
  })
  declare totalPrice: number

  @column({
    columnName: 'currentTotalPrice',
    consume: (value: string) => parseFloat(value),
  })
  declare currentTotalPrice: number

  @column()
  declare currency: string

  @column({ columnName: 'customerId' })
  declare customerId: string

  @column({ columnName: 'userId' })
  declare userId: string | null

  @column({ columnName: 'subtotalPrice' })
  declare subtotalPrice: number

  @column({
    columnName: 'currentSubtotalPrice',
    consume: (value: string) => parseFloat(value),
  })
  declare currentSubtotalPrice: number

  @column({ columnName: 'totalTax' })
  declare totalTax: number

  @column({
    columnName: 'currentTotalTax',
    consume: (value: string) => parseFloat(value),
  })
  declare currentTotalTax: number

  @column({ columnName: 'totalDiscounts' })
  declare totalDiscounts: number

  @column({
    columnName: 'currentTotalDiscounts',
    consume: (value: string) => parseFloat(value),
  })
  declare currentTotalDiscounts: number

  @column({ columnName: 'totalShipping' })
  declare totalShipping: number

  @column({ columnName: 'note' })
  declare note: string | null

  @belongsTo(() => ZnUser, {
    foreignKey: 'userId',
  })
  declare user: BelongsTo<typeof ZnUser>

  @column({ columnName: 'billingId' })
  declare billingId: string

  @belongsTo(() => ZnAddress, {
    foreignKey: 'billingId',
  })
  declare billing: BelongsTo<typeof ZnAddress>

  @belongsTo(() => ZnAddress, {
    foreignKey: 'billingId',
  })
  declare public billingAddress: BelongsTo<typeof ZnAddress>

  @column({ columnName: 'shippingId' })
  declare shippingId: string

  @belongsTo(() => ZnAddress, {
    foreignKey: 'shippingId',
  })
  declare shipping: BelongsTo<typeof ZnAddress>

  @belongsTo(() => ZnAddress, {
    foreignKey: 'shippingId',
  })
  declare public shippingAddress: BelongsTo<typeof ZnAddress>

  @hasMany(() => ZnOrderDetail, {
    foreignKey: 'orderId',
  })
  declare public orderDetails: HasMany<typeof ZnOrderDetail>

  @hasOne(() => ZnOrderDetail, {
    foreignKey: 'orderId',
  })
  declare public firstOrderDetail: HasOne<typeof ZnOrderDetail>

  @hasMany(() => ZnOrderDiscount, {
    foreignKey: 'orderId',
  })
  declare public orderDiscounts: HasMany<typeof ZnOrderDiscount>

  @hasOne(() => ZnAffiliateCommission, {
    foreignKey: 'orderId',
  })
  declare affliateCommmision: HasOne<typeof ZnAffiliateCommission>

  // @deprecated: Use 'fulfillments' instead
  @column({ columnName: 'trackingNumber' })
  declare trackingNumber: string

  // @deprecated: Use 'fulfillments' instead
  @column({ columnName: 'trackingCompany' })
  declare trackingCompany: string

  // @deprecated: Use 'fulfillments' instead
  @column({ columnName: 'trackingUrl' })
  declare trackingUrl: string

  @hasMany(() => ZnOrderFulfillment, {
    foreignKey: 'orderId',
  })
  declare fulfillments: HasMany<typeof ZnOrderFulfillment>

  @hasMany(() => ZnVendorOrder, {
    foreignKey: 'orderId',
  })
  declare vendorOrders: HasMany<typeof ZnVendorOrder>

  @hasOne(() => ZnTransaction, {
    foreignKey: 'orderId',
  })
  declare transaction: HasOne<typeof ZnTransaction>

  @column({ columnName: 'orderSource' })
  declare orderSource: string | null

  @column({ columnName: 'internalNote' })
  declare internalNote: string | null

  @computed()
  get zurnoStatus() {
    if (this.cancelledAt) {
      return 'cancelled'
    } else {
      if (this.closedAt) {
        return 'completed'
      }

      return 'ongoing'
    }
  }

  serializeExtras() {
    const newOrderDetails: ZnOrderDetail[] = []
    if (this.orderDetails) {
      for (const details of this.orderDetails) {
        if (details.currentQuantity > 0) {
          newOrderDetails.push(details)
        }
      }
    }

    return {
      orderDetailsCount: this.$extras.orderDetails_count,

      subtotalPrice: this.currentSubtotalPrice || this.subtotalPrice,
      totalDiscounts: this.currentTotalDiscounts || this.totalDiscounts,
      totalTax: this.currentTotalTax || this.totalTax,
      totalPrice: this.currentTotalPrice || this.totalPrice,

      orderDetails: newOrderDetails,
    }
  }
}
