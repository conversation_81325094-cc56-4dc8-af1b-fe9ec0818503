import {PineconeEmbeddingService} from "./pinecone_embedding_service.js";
import ZnProduct from "#models/zn_product";
import ZnCollection from "#models/zn_collection";
import {RecordMetadata} from "@pinecone-database/pinecone";
import {htmlToText} from "../../../services/commons.js";
import env from "#start/env";

export default class SparseProductCollectionService extends PineconeEmbeddingService<ZnProduct | ZnCollection> {

  constructor(
    indexName = env.get('SPARE_PRODUCT_COLLECTION_EMBEDDING_INDEX_NAME') ?? 'dumpster-product-spare',
    embeddingModel = 'pinecone-sparse-english-v0',
    batchSize = 64,
    useOwnEmbedding = false
  ) {
    super(indexName, embeddingModel, 1,batchSize, useOwnEmbedding);
  }

  protected buildMetadata(item: ZnProduct | ZnCollection): RecordMetadata {
    if (item instanceof ZnProduct) {
      const stockQty     = Math.max(item.variant?.inventoryQuantity ?? 0, 0)
      const reviewRating = Number(item.reviewSummary?.averageRating ?? 0)
      const reviewCount  = Number(item.reviewSummary?.totalReviews  ?? 0)
      const tags = item.tags.map( tag => tag.name) ?? []

      return {
        title         : item.title,
        vendor        : item.vendor?.companyName ?? '',
        itemType      : item.productType?.name ?? '',
        collectionIds : item.collections?.map(collection => collection.id) ?? [],
        tags          : tags,
        price         : Number(item.price) || 0,
        description   : this.buildText(item),
        inStock       : stockQty > 0,
        type          : 'product',
        stockQty,
        reviewRating,
        reviewCount,
      }
    }

    return {
      title: item.title ?? '',
      description: this.buildText(item),
      type : 'collection'
    }
  }

  protected buildText(item: ZnProduct | ZnCollection): string {
    if (item instanceof ZnProduct) {
      const title       = item.title ?? ''
      const vendor      = item.vendor?.companyName ?? ''
      const body        = htmlToText(item.description ?? '').trim()
      const tags = item.tags?.map( tag => tag.name) ?? []
      return [
        `Title: ${title} by ${vendor}.`,
        `Description: ${body}.`,
        `Tags: ${tags.join(', ')}`,
      ]
        .filter(Boolean)
        .join(' ')
    }

    return `Title: ${item.title ?? ''} \n Description: ${item.description ?? ''}\n`.trim()
  }

  protected getId(item: ZnProduct | ZnCollection): string {
    return item.id
  }

}
