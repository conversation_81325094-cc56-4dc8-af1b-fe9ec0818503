import DensePostEmbeddingService from "./dense_post_embedding_service.js";
import env from "#start/env";

export default class SparsePostEmbeddingService extends DensePostEmbeddingService {
  constructor(
    indexName = env.get('SPARE_POST_EMBEDDING_INDEX_NAME') ?? 'dumpster-post-spare',
    embeddingModel = 'pinecone-sparse-english-v0',
    batchSize = 4,
    useOwnEmbedding = false
  ) {
    super(indexName, embeddingModel, 1, batchSize, useOwnEmbedding)
  }
}
