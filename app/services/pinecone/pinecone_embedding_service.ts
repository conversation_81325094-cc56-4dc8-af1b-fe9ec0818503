import OpenAI from "openai";
import {Index, Pinecone, RecordMetadata, ServerlessSpecCloudEnum} from "@pinecone-database/pinecone";

export interface QueryResult {
  id: string,
  score: number,
  metadata?: RecordMetadata
}

export abstract class PineconeEmbeddingService<T> {
  protected readonly openai = new OpenAI()
  protected readonly pinecone = new Pinecone()
  protected readonly pineconeIndex  : Index

   protected constructor(
     protected readonly indexName : string,
     protected readonly embeddingModel : string,
     protected readonly dimension : number,
     protected readonly batchSize = 100,
     protected readonly useOwnEmbedding = true
  ) {
    this.pineconeIndex = this.pinecone.index(this.indexName)
  }

  protected abstract getId (item: T) : string
  protected abstract buildText (item: T): string
  protected abstract buildMetadata (item: T): RecordMetadata

  protected async openAIEmbed(texts: string[]): Promise<number[][]> {
    const { data } = await this.openai.embeddings.create({
      model : this.embeddingModel,
      input : texts,
    })

    return data.map(d => d.embedding)
  }

  async initializeIndex() {
    const { indexes } = await this.pinecone.listIndexes()
    if (!indexes?.some(index => index.name === this.indexName)) {
      if (this.useOwnEmbedding) {
        const defaultSpec = {
          name : this.indexName,
          dimension: this.dimension,
          metric : 'cosine',
          spec : {serverless : {cloud: 'aws', region: 'us-east-1'}},
          suppressConflicts: true,
          waitUntilReady: true,
        } as const
        await this.pinecone.createIndex(defaultSpec)
      } else {
        const defaultSpecModel = {
          name: this.indexName,
          cloud: 'aws' as ServerlessSpecCloudEnum,
          region: 'us-east-1',
          embed: {
            model: this.embeddingModel,
            fieldMap: { text: 'chunk_text' },
          },
          suppressConflicts: true,
          waitUntilReady: true
        }
        await this.pinecone.createIndexForModel(defaultSpecModel)
      }


      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  }

  private batchify(items: T[]){
    const batches = []
    for (let index = 0; index < items.length; index += this.batchSize) {
      batches.push(items.slice(index, index + this.batchSize))
    }
    return batches
  }

  async upsert(items: T[]) {
    const itemsBatches =  this.batchify(items)
    const pineconeRecords =  await  Promise.all(itemsBatches.map( async (batch ) => {
      const batchTexts = batch.map(item => this.buildText(item))
      const batchEmbeddings = this.useOwnEmbedding ? await this.openAIEmbed(batchTexts) : []
      if (this.useOwnEmbedding) {
        return  batch.map(  (item, itemIndex ) => ({
          id: this.getId(item),
          metadata: this.buildMetadata(item),
          values: batchEmbeddings[itemIndex]
        }))
      } else {
        return batch.map((item, itemIndex) => ({
          id:         this.getId(item),
          chunk_text: batchTexts[itemIndex],
          ...this.buildMetadata(item),
        }))
      }

      }
    ))

    type simpleRecord = {
      id: string,
      chunk_text: string,
    }

    await Promise.all(pineconeRecords.map(async (batch) => {
      if (this.useOwnEmbedding) {
        await this.pineconeIndex.upsert(batch)
      } else {
        const uploadableRecords = batch as simpleRecord[]
        await this.pineconeIndex.upsertRecords(uploadableRecords)
      }
    }))
  }

  async query(text: string, topK = 10, filter?: Record<string, unknown>): Promise<QueryResult[]> {
    const tStart = performance.now()

    const tEnsureStart = performance.now()
    const tEnsureEnd = performance.now()
    let matches: any[] = []
    let tEmbedStart: number | null = null
    let tEmbedEnd: number | null = null
    let tQueryStart: number | null = null
    let tQueryEnd: number | null = null
    let tSearchStart: number | null = null
    let tSearchEnd: number | null = null
    let tNormalizeStart: number | null = null
    let tNormalizeEnd: number | null = null

    if (this.useOwnEmbedding) {
      tEmbedStart = performance.now()
      const [queryEmbedding] = await this.openAIEmbed([text])
      tEmbedEnd = performance.now()

      tQueryStart = performance.now()
      const response = await this.pineconeIndex.query({
        vector: queryEmbedding,
        topK,
        includeMetadata: true,
        ...(filter && Object.keys(filter).length ? { filter } : {}),
      })
      tQueryEnd = performance.now()
      matches = response.matches
    } else {
      tSearchStart = performance.now()
      const response = await this.pineconeIndex.searchRecords({
        query: {
          inputs: { text },
          topK,
        },
      })
      tSearchEnd = performance.now()

      // Normalize shape to { id, score, ... } like the .query branch
      tNormalizeStart = performance.now()
      matches = response.result.hits.map((match: any) => ({
        ...match,
        id: match._id,
        score: match._score,
      }))
      tNormalizeEnd = performance.now()
    }

    const tMapStart = performance.now()
    // TODO: Check if this works for useOwnEmbedding, might have to use match.metadata in that case
    const results = matches.map((match: any) => ({
      id: match.id,
      score: match.score ?? 0,
      metadata: match.fields,
    }))
    const tMapEnd = performance.now()

    const tEnd = performance.now()

    const to2 = (n: number) => Math.round(n * 100) / 100
    console.log('timings_ms', {
      branch: this.useOwnEmbedding ? 'useOwnEmbedding' : 'serverSearch',
      ensureIndex: to2(tEnsureEnd - tEnsureStart),
      openAIEmbed: tEmbedStart && tEmbedEnd ? to2(tEmbedEnd - tEmbedStart) : null,
      pineconeQuery: tQueryStart && tQueryEnd ? to2(tQueryEnd - tQueryStart) : null,
      pineconeSearchRecords: tSearchStart && tSearchEnd ? to2(tSearchEnd - tSearchStart) : null,
      normalizeHits: tNormalizeStart && tNormalizeEnd ? to2(tNormalizeEnd - tNormalizeStart) : null,
      resultMapping: to2(tMapEnd - tMapStart),
      total: to2(tEnd - tStart),
      topK,
      hasFilter: !!(filter && Object.keys(filter).length),
    })

    return results
  }

  async deleteIndex() {
    await this.pinecone.deleteIndex(this.indexName)
  }

}
