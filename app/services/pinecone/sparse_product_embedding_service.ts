import env from "#start/env";
import ProductEmbeddingService from "./product_embedding_service.js";

export default class SparseProductEmbeddingService extends ProductEmbeddingService {
  constructor(
    indexName = env.get('SPARE_PRODUCT_EMBEDDING_INDEX_NAME') ?? 'dumpster-product-spare',
    embeddingModel = 'pinecone-sparse-english-v0',
    batchSize = 64,
    useOwnEmbedding = false
  ) {
    super(indexName, embeddingModel, 1,batchSize, useOwnEmbedding)
  }
}
