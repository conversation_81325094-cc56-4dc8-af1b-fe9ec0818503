import { EPaymentType } from "#constants/payment_type";
import ZnAchTransferDetail from "#models/zn_ach_transfer_detail";
import ZnAffiliate from "#models/zn_affiliate";
import ZnDirectDepositDetail from "#models/zn_direct_deposit_detail";
import ZnOtherPaymentDetail from "#models/zn_other_payment_detail";
import ZnPaymentMethod from "#models/zn_payment_method";
import ZnPaypalDetail from "#models/zn_paypal_detail";
import logger from "@adonisjs/core/services/logger";

export default class AffiliationPaymentMethodService {
  async getAllPaymentMethods(affiliateId: string) {
    const paymentMethods = await ZnPaymentMethod.query()
      .whereHas('affiliates', (query) => {
        query.where('id', affiliateId);
      })
      .preload('paypalDetail')
      .preload('directDepositDetail')
      .preload('achTransferDetail')
      .preload('otherPaymentDetail');
    return [
      ...paymentMethods.filter((pm) => pm.paymentType === EPaymentType.PAYPAL),
      ...paymentMethods.filter((pm) => pm.paymentType === EPaymentType.DIRECT_DEPOSIT),
      ...paymentMethods.filter((pm) => pm.paymentType === EPaymentType.ACH_TRANSFER),
      ...paymentMethods.filter((pm) => pm.paymentType === EPaymentType.OTHER),
    ]
  }

  async getPaymentMethodById(paymentMethodId: string) {
    const paymentMethod = await ZnPaymentMethod.findOrFail(paymentMethodId);

    if (paymentMethod.paymentType == EPaymentType.PAYPAL)
      await paymentMethod.load('paypalDetail');
    else if (paymentMethod.paymentType == EPaymentType.DIRECT_DEPOSIT)
      await paymentMethod.load('directDepositDetail');
    else if (paymentMethod.paymentType == EPaymentType.ACH_TRANSFER)
      await paymentMethod.load('achTransferDetail');
    else
      await paymentMethod.load('otherPaymentDetail');

    return paymentMethod;
  }

  async createPaymentMethod(affiliateId: string, payload: any) {
    const affiliate = await ZnAffiliate.query()
      .where('id', affiliateId)
      .preload('paymentMethods', (query) => {
        query.where('paymentType', payload.paymentType);
      })
      .firstOrFail();

    if (affiliate.paymentMethods.length === 0) {
      const newPaymentMethod = await ZnPaymentMethod.create({
        paymentType: payload.paymentType,
        isDefault: true
      });

      await affiliate.related('paymentMethods').attach([newPaymentMethod.id]);
      await affiliate.load('paymentMethods', (query) => {
        query.where('paymentType', payload.paymentType);
      })
    }

    switch (payload.paymentType) {

      case EPaymentType.PAYPAL:
        if (!payload.paypalEmail) throw new Error('PayPal email is required')

        const existingPaypalMethod = await ZnPaypalDetail.findBy('paymentMethodId', affiliate.paymentMethods[0].id)
        if (existingPaypalMethod) {
          throw new Error('Paypal method has already been added');
        }

        await ZnPaypalDetail.create({
          paymentMethodId: affiliate.paymentMethods[0].id,
          paypalEmail: payload.paypalEmail,
          legalName: payload.legalName,
          countryCode: payload.countryCode,
        })
        break

      case EPaymentType.DIRECT_DEPOSIT:
        if (!payload.accountName || !payload.routingNumber || !payload.accountNumber || !payload.accountType) {
          throw new Error('Direct deposit information is not fully provided')
        }

        const directDepositDetail = await ZnDirectDepositDetail.findBy('paymentMethodId', affiliate.paymentMethods[0].id)
        if (directDepositDetail) {
          throw new Error('Direct deposit method has already been added');
        }

        await ZnDirectDepositDetail.create({
          paymentMethodId: affiliate.paymentMethods[0].id,
          accountName: payload.accountName,
          routingNumber: payload.routingNumber,
          accountNumber: payload.accountNumber,
          accountType: payload.accountType,
          bankName: payload.bankName,
          accountHolderAddress: payload.accountHolderAddress,
        })
        break

      case EPaymentType.ACH_TRANSFER:
        if (!payload.accountName || !payload.routingNumber || !payload.accountNumber || !payload.accountType) {
          throw new Error('All ACH Transfer information are required')
        }

        const existingACHMethod = await ZnAchTransferDetail.findBy('paymentMethodId', affiliate.paymentMethods[0].id)
        if (existingACHMethod) {
          throw new Error('ACH method has already been added');
        }

        await ZnAchTransferDetail.create({
          paymentMethodId: affiliate.paymentMethods[0].id,
          accountName: payload.accountName,
          routingNumber: payload.routingNumber,
          accountNumber: payload.accountNumber,
          accountType: payload.accountType
        })
        break

      default:
        if (!payload.note) {
          throw new Error('Additional information is required')
        }

        const existingOtherMethod = await ZnOtherPaymentDetail.findBy('paymentMethodId', affiliate.paymentMethods[0].id)
        if (existingOtherMethod) {
          throw new Error('The other payment method has already been added');
        }

        await ZnOtherPaymentDetail.create({
          paymentMethodId: affiliate.paymentMethods[0].id,
          note: payload.note
        })
    }

    const isDefault = (payload.isDefault !== null && payload.isDefault !== undefined) ? payload.isDefault : false
    await this.setDefaultMethodOnCreate(affiliate, affiliate.paymentMethods[0].id, isDefault)

    for (const paymentMethod of affiliate.paymentMethods) {
      if (paymentMethod.paymentType == EPaymentType.PAYPAL)
        await paymentMethod.load('paypalDetail')
      else if (paymentMethod.paymentType == EPaymentType.DIRECT_DEPOSIT)
        await paymentMethod.load('directDepositDetail')
      else if (paymentMethod.paymentType == EPaymentType.ACH_TRANSFER)
        await paymentMethod.load('achTransferDetail')
      else
        await paymentMethod.load('otherPaymentDetail')
    }

    logger.info('Added payment method. Current payment methods: %s', JSON.stringify(affiliate.paymentMethods, null, 2))
    return affiliate.paymentMethods;
  }

  async updatePaymentMethod(paymentMethodId: string, payload: any) {
    let paymentMethod = await ZnPaymentMethod.findOrFail(paymentMethodId);

    switch (paymentMethod.paymentType) {

      case EPaymentType.PAYPAL:
        const paypalDetail = await ZnPaypalDetail.findByOrFail('paymentMethodId', paymentMethodId)
        paypalDetail.paypalEmail = payload.paypalEmail ?? paypalDetail.paypalEmail;
        paypalDetail.legalName = payload.legalName ?? paypalDetail.legalName;
        paypalDetail.countryCode = payload.countryCode ?? payload.countryCode;
        await paypalDetail.save();
        break

      case EPaymentType.DIRECT_DEPOSIT:
        const directDepositDetail = await ZnDirectDepositDetail.findByOrFail('paymentMethodId', paymentMethodId)
        directDepositDetail.accountName = payload.accountName ?? directDepositDetail.accountName;
        directDepositDetail.routingNumber = payload.routingNumber ?? directDepositDetail.routingNumber;
        directDepositDetail.accountNumber = payload.accountNumber ?? directDepositDetail.accountNumber;
        directDepositDetail.accountType = payload.accountType ?? directDepositDetail.accountType;
        directDepositDetail.bankName = payload.bankName ?? directDepositDetail.bankName;
        directDepositDetail.accountHolderAddress = payload.accountHolderAddress ?? directDepositDetail.accountHolderAddress;
        await directDepositDetail.save()
        break

      case EPaymentType.ACH_TRANSFER:
        const achTransferDetail = await ZnAchTransferDetail.findByOrFail('paymentMethodId', paymentMethodId)
        achTransferDetail.accountName = payload.accountName ?? achTransferDetail.accountName;
        achTransferDetail.routingNumber = payload.routingNumber ?? achTransferDetail.routingNumber;
        achTransferDetail.accountNumber = payload.accountNumber ?? achTransferDetail.accountNumber;
        achTransferDetail.accountType = payload.accountType ?? achTransferDetail.accountType;
        await achTransferDetail.save()
        break

      default:
        const otherPaymentDetail = await ZnOtherPaymentDetail.findByOrFail('paymentMethodId', paymentMethodId)
        otherPaymentDetail.note = payload.note ?? otherPaymentDetail.note;
        await otherPaymentDetail.save()
        break
    }

    const newIsDefaultStatus = (payload.isDefault !== null && payload.isDefault !== undefined) ? payload.isDefault : false
    if (paymentMethod.isDefault !== newIsDefaultStatus) {
      if (newIsDefaultStatus)
        paymentMethod = await this.setDefaultMethod(paymentMethodId);
      else
        paymentMethod = await this.unsetDefaultMethod(paymentMethodId);
    }

    if (paymentMethod.paymentType == EPaymentType.PAYPAL)
      await paymentMethod.load('paypalDetail')
    else if (paymentMethod.paymentType == EPaymentType.DIRECT_DEPOSIT)
      await paymentMethod.load('directDepositDetail')
    else if (paymentMethod.paymentType == EPaymentType.ACH_TRANSFER)
      await paymentMethod.load('achTransferDetail')
    else
      await paymentMethod.load('otherPaymentDetail')
    logger.info('Updated payment method: %s', JSON.stringify(paymentMethod, null, 2))
    return paymentMethod
  }

  async setDefaultMethod(paymentMethodId: string) {
    const targetPaymentMethod = await ZnPaymentMethod.query()
      .where('id', paymentMethodId)
      .preload('affiliates')
      .firstOrFail();

    if (targetPaymentMethod.affiliates.length === 0) {
      targetPaymentMethod.isDefault = true;
      await targetPaymentMethod.save();
      return targetPaymentMethod;
    }

    const allMethods = await ZnPaymentMethod.query()
      .whereHas('affiliates', (query) => {
        query.where('id', targetPaymentMethod.affiliates[0].id);
      })
      .preload('paypalDetail')
      .preload('directDepositDetail')
      .preload('achTransferDetail')
      .preload('otherPaymentDetail');

    for (const method of allMethods) {
      method.isDefault = (method.id == paymentMethodId);
      await method.save();
    }

    await targetPaymentMethod.refresh();
    return targetPaymentMethod;
  }

  async unsetDefaultMethod(paymentMethodId: string) {
    const targetPaymentMethod = await ZnPaymentMethod.query()
      .where('id', paymentMethodId)
      .preload('affiliates')
      .firstOrFail();

    if (targetPaymentMethod.affiliates.length === 0) {
      targetPaymentMethod.isDefault = true;
      await targetPaymentMethod.save();
      return targetPaymentMethod;
    }

    const anotherMethod = await ZnPaymentMethod.query()
      .whereHas('affiliates', (query) => {
        query.where('id', targetPaymentMethod.affiliates[0].id);
      })
      .whereNot('id', paymentMethodId)
      .preload('paypalDetail')
      .preload('directDepositDetail')
      .preload('achTransferDetail')
      .preload('otherPaymentDetail')
      .first();

    if (anotherMethod) {
      anotherMethod.isDefault = true;
      await anotherMethod.save();

      targetPaymentMethod.isDefault = false;
      await targetPaymentMethod.save();
    }

    return targetPaymentMethod;
  }

  async deletePaymentMethodId(paymentMethodId: string) {
    const paymentMethod = await ZnPaymentMethod.query()
      .where('id', paymentMethodId)
      .preload('affiliates')
      .firstOrFail();

    if (paymentMethod.isDefault) {
      const anotherMethod = await ZnPaymentMethod
        .query()
        .whereNot('paymentType', paymentMethod.paymentType)
        .whereHas('affiliates', (query) => {
          query.where('id', paymentMethod.affiliates[0].id);
        })
        .first();
      if (anotherMethod) {
        anotherMethod.isDefault = true
        await anotherMethod.save()
        logger.info(`Set payment method ${anotherMethod.paymentType} as default method.`);
      }
    }

    await paymentMethod.softDelete();

    const message = `Payment method ${paymentMethod.paymentType} has been deleted successfully.`
    logger.info(message)
    return message;
  }

  private async setDefaultMethodOnCreate(affiliate: ZnAffiliate, currentPaymentMethodId: string, isDefault: boolean) {
    await affiliate.load('paymentMethods');

    if (affiliate.paymentMethods.length === 1) {
      affiliate.paymentMethods[0].isDefault = true;
      await affiliate.save();
      return affiliate.paymentMethods[0];
    }

    if (!isDefault) {
      return affiliate.paymentMethods[0];
    }

    for (const method of affiliate.paymentMethods) {
      method.isDefault = (method.id == currentPaymentMethodId)
      await method.save();
    }

    return affiliate.paymentMethods;
  }
}