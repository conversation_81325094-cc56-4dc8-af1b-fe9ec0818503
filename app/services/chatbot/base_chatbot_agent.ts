import {<PERSON><PERSON><PERSON><PERSON>, AgentResponse, Cha<PERSON>botA<PERSON>} from "#services/chatbot/chatbot_agent_interface";
import {EAIAssistantRole} from "#models/zn_ai_assistant";
import OpenAI from "openai";
import {IvsChatBotService} from "#services/aws/ivschat_bot_service";
import {AgentSettings} from "./chatbot_agent_interface.js";

export const EMPTY_MESSAGE_HOLDER = 'FINDING PRODUCT'

export abstract class BaseChatbotAgent implements ChatbotAgent {

  abstract readonly role: EAIAssistantRole

  protected constructor(
    protected readonly assistantId: string,
    protected readonly openAIId: string,
    protected readonly instruction: string,
    protected readonly agentSettings: AgentSettings,
    protected readonly historyLimit = 4
  ) {}


  protected openai = new OpenAI()
  protected ivsChatbotService = new IvsChatBotService()


  needsPreContext() { return false }
  async buildPreContext(_init: AgentInit) { return ''}
  needsPostProcess(_firstResponse: AgentResponse) { return false }
  takeNextTurn(_firstResponse: AgentResponse) { return false }

  private async getRecentConversation(threadId: string, limit = 10) {
    const response = await this.openai.beta.threads.messages.list(threadId, {
      limit: limit
    })
    let sawLatestUserText = false
    let userMessage : string = ""

    const previousConversation = response.data.map( (message) => {
      const role = message.role
      const messageContent = message.content[0] as any
      const textMessage = messageContent.text.value as string ?? ""
      if (!sawLatestUserText) {
        if (role == 'user') {
          sawLatestUserText = true
          userMessage = textMessage
          return ''
        }
        return 'Context: ' + textMessage
      }
      return role + ": " + textMessage
    })
    return {
      previousConversation: previousConversation,
      userMessage: userMessage,
    }
  }

  protected async runAssistant(threadId: string): Promise<AgentResponse> {
    const {previousConversation, userMessage} = await this.getRecentConversation(threadId, this.historyLimit)
    console.log("Previous conversation", previousConversation)
    const chatResponse = await this.openai.responses.create({
      model: this.agentSettings.model,
      input: userMessage,
      instructions : this.instruction +
        "This is the previous conversation:" + previousConversation
        + "\n Today is:" + new Date().toLocaleDateString("en-US", {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric"
      }),
      tools: this.agentSettings.tools,
      max_output_tokens: 10000,
      text: this.agentSettings.text ?  this.agentSettings.text : {},
      reasoning: this.agentSettings.reasoning ?  this.agentSettings.reasoning : {},
    })

    const assistantResponse = await this.getCompleteResponse(userMessage, chatResponse.output_text, this.assistantId)
    await this.openai.beta.threads.messages.create(threadId,{
      role: 'assistant',
      content: assistantResponse.text
    })

    console.log('Final response', assistantResponse)
    return assistantResponse
  }

  protected async getQuestions(lastMessage: string) {
    const chatResponse = await this.openai.responses.create({
      model: 'gpt-4.1-mini-2025-04-14',
      instructions: "Generate 3 first-person questions in the input language (as if the reader is asking). " +
        "Rewrite any second-person phrasing to first-person, adjusting grammar (e.g., you/your → I/my; Bạn → Tôi)." +
        "Output only the questions seperated by ;",
      input: lastMessage,
      temperature: 0.1
    })
    const parseQuestions = (aiResponse: string) => {
      return aiResponse
        .split(';')
        .map(question => question.trim())
        .filter(question => question.length > 0)
        .map(question => question.replace(/^\s*\d+\s*[.)]\s*/, ""))
    }
    return parseQuestions(chatResponse.output_text)
  }

  // This should be overridden by the subclass in most cases
  protected async getCompleteResponse( _userMessage: string, lastMessage: string, assistantId: string) : Promise<AgentResponse> {
   return {
     text: lastMessage,
     productIds: [],
     collectionIds: [],
     postIds: [],
     questions: [],
     assistantId: assistantId,
     action: []
   }
  }

  async getInitialResponse(init: AgentInit): Promise<AgentResponse> {
    const firstResponse = await this.runAssistant(init.threadId)
    firstResponse.assistantId = this.assistantId
    return firstResponse
  }

  abstract postProcess(first: AgentResponse, init: AgentInit): Promise<AgentResponse| void>

}




