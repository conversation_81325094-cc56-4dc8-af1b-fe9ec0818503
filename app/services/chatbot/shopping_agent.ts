import {BaseChatbotAgent, EMPTY_MESSAGE_HOLDER} from "#services/chatbot/base_chatbot_agent";
import {EAIAssistantRole} from "#models/zn_ai_assistant";
import {AgentInit, AgentResponse} from "#services/chatbot/chatbot_agent_interface";
import RecommendationEngine from "#services/chatbot/recommendation/recommendation_engine";
import {AgentSettings} from "./chatbot_agent_interface.js";
import {loadPrompt} from "./prompt_loader.js";
import env from "#start/env";
import {Tool} from "openai/resources/responses/responses";

export default class ShoppingAgent extends BaseChatbotAgent {
  readonly role = EAIAssistantRole.SHOPPING_ASSISTANT
  protected recommendationEngine = new RecommendationEngine()

  constructor(assistantId: string, openAIAssistantId: string) {
    // TODO: Refactor vector_file_id init
    const vectorFileId = env.get('SHOPPING_ASSISTANT_FILE_ID')
    let tools : Tool[] = []
    if (vectorFileId) {
      tools.push(
        {
          type: "file_search",
          vector_store_ids: [vectorFileId],
        }
      )
    }
    const agentSettings: AgentSettings = {
      model: "gpt-5",
      reasoning: {
        effort: "low"
      },
      text: {
        verbosity: "low"
      },
      tools
    }
    const instruction = loadPrompt('shopping_agent')
    const historyLimit = 4

    super(assistantId, openAIAssistantId,instruction, agentSettings,historyLimit)
  }

  private async getRecommendationsIds(userMessage: string, agentResponse: string){
    const chatResponse = await this.openai.responses.create(
      {
        model: "gpt-4.1-mini",
        input: 'User:' + userMessage + 'agent: ' + agentResponse,
        instructions: "Given the latest ‘Users’ and ‘Assistant’ messages about products, return ONE line of deduplicated, semicolon-separated search keywords (1–4 words each). \n" +
          "Include brand, collection/model names, product types, and tools mentioned. Keep original language. No extra text. Extract at most 4 keywords"
      }
    )
    return chatResponse.output_text.split(";")
  }

  protected async getCompleteResponse(userMessage: string, lastMessage: string, assistantId: string) : Promise<AgentResponse> {
    const [questions, descriptions] = await Promise.all([
      this.getQuestions(lastMessage),
      this.getRecommendationsIds(userMessage, lastMessage)
    ])

    return {
      text: lastMessage,
      description: descriptions,
      productIds: [],
      collectionIds: [],
      postIds: [],
      questions: questions,
      assistantId: assistantId,
      action: []
    }
  }

  needsPreContext(): boolean { return false }

  needsPostProcess(firstResponse: AgentResponse) {
    return !!(firstResponse as any).description
  }

  async postProcess(firstResponse: AgentResponse, _init: AgentInit): Promise<AgentResponse> {
    const description = (firstResponse as any).description ?? [""] as string[]
    const recommendations = await this.recommendationEngine.run({
      descriptions :{
        product_collection: description
      }
    })

    let productRecommendations : string[] = []
    let collectionRecommendations : string[] = []

    for (const recommendation of recommendations.product_collection ?? []) {
      if (recommendation.startsWith("product-")) {
        productRecommendations.push(recommendation.replace("product-", ""))
      } else if (recommendation.startsWith("collection-")) {
        collectionRecommendations.push(recommendation.replace("collection-", ""))
      }
    }


    return {
      text: EMPTY_MESSAGE_HOLDER,
      productIds: productRecommendations ?? [],
      collectionIds: collectionRecommendations ?? [],
      questions: firstResponse.questions ?? [],
      assistantId: this.openAIId,
    }
  }
}
