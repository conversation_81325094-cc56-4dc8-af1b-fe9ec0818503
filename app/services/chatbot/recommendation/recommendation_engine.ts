import ProductRecommender from "#services/chatbot/recommendation/product_recommender";
import CollectionRecommender from "#services/chatbot/recommendation/collection_recommender";
import PostRecommeder from "#services/chatbot/recommendation/post_recommeder";
import RecommendationFitEvaluator from "./recommendation_fit_evaluator.js";
import SparePostRecommender from "./spare_post_recommender.js";
import SparseProductCollectionRecommender from "./sparse_product_collection_recommender.js";

export interface VectorDatabaseMatch {
  id: string
  score: number
  metadata: Record<string, any>
}

export interface EmbeddingService {
  query(text: string, k: number, filter?: any): Promise<any[]>
}

export interface Recommender {
  query(sentence: string, k: number, constraints?: any): Promise<VectorDatabaseMatch[]>
  evaluate?(sentence: string, metadata: any): Promise<boolean>
}

export type RecommendationKind = 'product' | 'collection' | 'post' | 'search_post' | 'product_collection'


export interface RecommendationEngineInput {
  descriptions: Partial<Record<RecommendationKind, string | string[]>>
  constraints?: Partial<Record<RecommendationKind, Record<string, any>>>
  maxTotal?: number
}
export type RecommendationEngineOutput = Record<RecommendationKind, string[]>
export default class RecommendationEngine {

  private readonly fitEvaluator = new RecommendationFitEvaluator()
  private readonly productRecommender = new ProductRecommender()
  private readonly collectionRecommender = new CollectionRecommender()
  private readonly postRecommender = new PostRecommeder()
  private readonly sparePostRecommender = new SparePostRecommender()
  private readonly productCollectionRecommender = new SparseProductCollectionRecommender()

  protected readonly RECOMMENDER_REGISTRY: Record<RecommendationKind, Recommender>

  constructor(
    private readonly DEFAULT_MAX_TOTAL = 10
  ) {
    this.RECOMMENDER_REGISTRY = {
      product: {
        query: (description, k, constraints) =>
          this.productRecommender.query(description, k, constraints ?? {}),
        evaluate: (description: string, metadata: { description: string }) =>
          this.fitEvaluator.isFit(description, metadata.description),
      },
      collection: {
        query: (description, k, constraints) =>
          this.collectionRecommender.query(description, k, constraints),
        evaluate: async () => true,
      },
      product_collection: {
        query: (description, k) =>
          this.productCollectionRecommender.query(description, k ),
      },
      post: {
        query: (description, k, constraints) =>
          this.postRecommender.query(description, k, constraints),
        evaluate: (description: string, metadata: { description: string }) =>
          this.fitEvaluator.isFit(description, metadata.description),
      },
      search_post: {
        query: (description, k, constraints) =>
          this.sparePostRecommender.query(description, k, constraints),
      },
    }
  }

  private async getIDs(
    descriptions: string[],
    perDescQuota: number,
    maxTotal: number,
    recommender: Recommender,
    checkRecommendation: boolean,
    kind : RecommendationKind,
    constraints?: any,
  ): Promise<string[]> {

    const chosen: string[] = []

    console.log('Recommender type', typeof(recommender))
    for (const description of descriptions) {
      if (chosen.length > maxTotal) break
      const matches = await recommender.query(description, perDescQuota + 1, constraints)
      let numberOfAddedRecommendation = 0
      for (const match of matches) {
        if (chosen.includes(match.id)) continue
        if (checkRecommendation &&
          recommender.evaluate &&
          !(await recommender.evaluate(description, match.metadata))
        ) continue

        if (kind === 'product_collection') {
          const type = String(match.metadata.type ?? "")
          chosen.push(type + '-' + match.id)
        } else {
          chosen.push(match.id)
        }
        numberOfAddedRecommendation++
        if (numberOfAddedRecommendation >= perDescQuota || chosen.length >= maxTotal) break
      }
    }
    return chosen
  }

  async run(input: RecommendationEngineInput,runPostEvaluation : boolean = true): Promise<RecommendationEngineOutput> {
    const maxTotal = input.maxTotal ?? this.DEFAULT_MAX_TOTAL
    const output : RecommendationEngineOutput = Object.create(null)
    for (const [kind, recommender] of Object.entries(this.RECOMMENDER_REGISTRY) as [RecommendationKind, Recommender][]) {
      const descriptions = this.toArray(input.descriptions?.[kind])
      if (descriptions.length < 1) continue
      output[kind] = await this.getIDs(
        descriptions,
        this.getQuotaForDescription(descriptions.length, maxTotal),
        maxTotal,
        recommender,
        runPostEvaluation,
        kind,
        input.constraints?.[kind],
      )
    }
    return output
  }

  private toArray  (x?: string | string[]) {
    return !x ? [] : Array.isArray(x) ? x : [x]
  }

  private getQuotaForDescription(numberDescriptions: number, maxTotal: number) {
    return Math.max(Math.ceil(maxTotal / Math.max(1, numberDescriptions)), 1)
  }
}
