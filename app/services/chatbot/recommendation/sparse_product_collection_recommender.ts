import {BaseRecommender} from "./base_recommender.js";
import SparseProductCollectionService from "../../pinecone/sparse_product_collection_embedding_service.js";

// This only supports for Chatbot product
export default class SparseProductCollectionRecommender extends BaseRecommender {
  protected embeddingService = new SparseProductCollectionService()

  async query(
    sentence: string,
    k = 10,
  ) {
    return super.query(sentence, k)
  }
}
