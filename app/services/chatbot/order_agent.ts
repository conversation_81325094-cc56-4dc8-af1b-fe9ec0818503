import {BaseChatbotAgent} from "#services/chatbot/base_chatbot_agent";
import {EAIAssistantRole} from "#models/zn_ai_assistant";
import OpenAI from "openai";
import {AgentInit, AgentResponse} from "#services/chatbot/chatbot_agent_interface";
import ZnOrder from "#models/zn_order";
import ZnAddress from "#models/zn_address";
import {loadPrompt} from "#services/chatbot/prompt_loader";
import {sanitizeAssistantText} from "./utils.js";
import {AgentSettings} from "./chatbot_agent_interface.js";
import env from "#start/env";
import {Tool} from "openai/resources/responses/responses";
import ZnOrderFulfillment from "#models/zn_order_fulfillment";
import ZnOrderDetail from "#models/zn_order_detail";

function formatAddress(address? : ZnAddress | null) {
  if (!address) return 'n/a'
  const adr2 = address.address2 ? `, ${address.address2} ` : ''
  return `${address.name}, ${address.address1}${adr2}, ${address.city}, ` +
    `${address.province ?? ''} ${address.zip ?? ''}, ${address.country}`
}

function formatMoney(amount: number ) {
  return `$${amount.toFixed(2)}`
}

function formatTrack(id?: string | null, carrier? : string | null, url?: string | null) {
  const parts : string[] = []
  if (id) parts.push(`Tracking ID: ${id}`)
  if (carrier) parts.push(`Carrier: ${carrier}`)
  if (url) parts.push(`Url: ${url}`)
  return parts.length ? `  • Tracking → ${parts.join(' | ')}` : '  • Tracking → n/a'
}

export default class OrderAgent extends BaseChatbotAgent {
  readonly role = EAIAssistantRole.ORDER_ASSISTANT
  constructor( assistantId: string, openAIId: string) {
    const instruction = loadPrompt('order_agent')
    const vectorFileId = env.get('ORDER_ASSISTANT_FILE_ID')
    let tools : Tool[] = []
    if (vectorFileId) {
      tools.push(
        {
          type: "file_search",
          vector_store_ids: [vectorFileId],
        }
      )
    }
    const agentSettings: AgentSettings = {
      model: "gpt-5-mini",
      reasoning: {
        effort: "low"
      },
      text: {
        verbosity: "low"
      },
      tools
    }
    const historyLimit = 10
    super(assistantId, openAIId, instruction, agentSettings, historyLimit)
    this.openai = new OpenAI()
  }

  needsPreContext() { return true }

  async buildPreContext(init: AgentInit) {
    const filters = await this.extractFilters(init.userMessage)
    const orders  = await this.buildOrderQuery(init.userId, filters)
    let ordersFullfillments : Record<string, string[]> = {}
    for (const order of orders) {
      const fulfillments = await ZnOrderFulfillment
        .query()
        .where('orderId', order.id)
        .preload('orderDetails')

      ordersFullfillments[order.id] = await Promise.all(
        fulfillments.map(async (fulfillment) => {
          const orderDetails = fulfillment.orderDetails as ZnOrderDetail[]
          const trackingStatus = await fulfillment.trackingStatus()
          const orderDetailsString = orderDetails.map(orderDetail => {
            const title = orderDetail.title
            const quantity = orderDetail.quantity
            const price = orderDetail.price
            return "Product: " + title + " Quantity: " + quantity + " Price: " + price
          }).join('; ')
          return orderDetailsString + "Tracking status: " + trackingStatus
        })
      )
    }

    if (orders.length === 0) return 'The user has no orders matching the request.'

    const context = orders.map((order) => {
      const placed  = order.createdAt.toISO()
      const ship    = formatAddress(order.shippingAddress)
      const bill    = formatAddress(order.billingAddress)

      const orderInfo = [
        `• ${order.name}  (ID: ${order.id})`,
        `  • Placed: ${placed}`,
        `  • Status: ${order.status}`,
        `    • Financial: ${order.financialStatus}`,
        `    • Fulfillment: ${order.fulfillmentStatus ?? 'unfulfilled'}`,
        `  • Total: ${formatMoney(order.totalPrice)}`,
        `  • Shipping → ${ship}`,
        `  • Billing  → ${bill}`,
        formatTrack(order.trackingNumber, order.trackingCompany, order.trackingUrl),
      ]

      const fulfillments = ordersFullfillments[order.id]
      if (fulfillments && fulfillments.length > 0) {
        orderInfo.push('  • Fulfillments:')
        fulfillments.forEach((fulfillment, index) => {
          orderInfo.push(`    ${index + 1}. ${fulfillment}`)
        })
      }

      return orderInfo.join('\n')
    })

    return ['Here are the user’s orders:', ...context].join('\n')
  }

  private async extractFilters(content: string) {
    const orderFilterSystemPrompt = await loadPrompt("order_tag_filter")
    try {
      const response = await this.openai.chat.completions.create({
        model:  'gpt-4.1',
        temperature: 0.1,
        messages: [
          {
            role: 'system',
            content: orderFilterSystemPrompt
          },
          {role: 'user', content}
        ]
      })
      const receivedMessage = response.choices?.[0]?.message?.content || content
      return sanitizeAssistantText(receivedMessage)
    } catch (error) {
      console.log(error)
      return {}
    }

  }

  protected async getCompleteResponse(userMessage: string, lastMessage: string, assistantId: string) : Promise<AgentResponse> {
    const [questions, shouldEscalate] = await Promise.all([
      this.getQuestions(lastMessage),
      this.shouldEscalate(userMessage,lastMessage)
    ])

    let actions : string[] = []
    if (shouldEscalate==='yes') {
      actions.push('escalate')
    }
    return {
      text: lastMessage,
      productIds: [],
      collectionIds: [],
      postIds: [],
      questions: questions,
      assistantId: assistantId,
      action: actions
    }
  }

  protected async shouldEscalate(userMessage: string, lastMessage: string) {
    const chatResponse = await this.openai.responses.create({
      model: 'gpt-4.1',
      instructions: "Assistant response: " + lastMessage + "User Message: " + userMessage,
      input: "Determine if the user needs to see the customer service agent. Returns ONE WORD, either yes or no ",
      temperature: 0.1
    })
    console.log('Should Escalate' , chatResponse)
    return chatResponse.output_text
  }

  private buildOrderQuery(userId: string, filter: any) {
    const query = ZnOrder
      .query()
      .where('userId', userId)

    if (filter.order_numbers?.length)      query.whereIn('name', filter.order_numbers)
    if (filter.order_status)               query.where('status', filter.order_status)
    if (filter.financial_status)           query.where('financialStatus', String(filter.financial_status))
    if (filter.fulfillment_status)         query.where('fulfillmentStatus', String(filter.fulfillment_status))
    if (filter.total_price?.min !== undefined) query.where('totalPrice', '>=', filter.total_price.min)
    if (filter.total_price?.max !== undefined) query.where('totalPrice', '<=', filter.total_price.max)
    if (filter.fulfilled_at?.from)         query.where('createdAt', '>=', filter.fulfilled_at.from)
    if (filter.fulfilled_at?.to)           query.where('createdAt', '<=', filter.fulfilled_at.to)

    return query
      .preload('shippingAddress')
      .preload('billingAddress')
      .preload('firstOrderDetail')
      .preload('orderDetails')
      .preload('fulfillments')
      .orderBy('createdAt', 'desc')
  }


  needsPostProcess(_firstResponse: AgentResponse) {return false}

  async postProcess(_firstResponse: AgentResponse) {}

}
