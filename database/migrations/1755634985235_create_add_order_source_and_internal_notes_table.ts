import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_orders'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('orderSource')
      table.text('internalNote')
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('internalNote')
      table.dropColumn('orderSource')
    })
  }
}