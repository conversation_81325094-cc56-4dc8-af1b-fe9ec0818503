import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_users'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('email').nullable().alter()
      table.string('firstName').nullable().alter()
      table.string('lastName').nullable().alter()
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('email').notNullable().alter()
      table.string('firstName').notNullable().alter()
      table.string('lastName').notNullable().alter()
    })
  }
}